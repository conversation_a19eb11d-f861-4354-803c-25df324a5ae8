//+------------------------------------------------------------------+
//|                                              Test_XAUUSD_EA.mq5 |
//|                                  Copyright 2024, TradingAI_MQ5 |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, TradingAI_MQ5"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property script_show_inputs

//--- Input parameters
input int TestBars = 1000;        // Number of bars to test
input bool ShowDetails = true;    // Show detailed output

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== XAUUSD EA Testing Script ===");
   
   //--- Test 1: Check symbol information
   TestSymbolInfo();
   
   //--- Test 2: Check indicator creation
   TestIndicators();
   
   //--- Test 3: Test calculation functions
   TestCalculations();
   
   //--- Test 4: Test market conditions
   TestMarketConditions();
   
   Print("=== Testing Complete ===");
}

//+------------------------------------------------------------------+
//| Test symbol information                                          |
//+------------------------------------------------------------------+
void TestSymbolInfo()
{
   Print("\n--- Testing Symbol Information ---");
   
   string symbol = "XAUUSD";
   
   //--- Check if symbol exists
   if(!SymbolSelect(symbol, true))
   {
      Print("ERROR: Symbol ", symbol, " not found!");
      return;
   }
   
   //--- Get symbol properties
   double point = SymbolInfoDouble(symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(symbol, SYMBOL_DIGITS);
   double tick_size = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_SIZE);
   double tick_value = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_VALUE);
   double min_lot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
   
   Print("Symbol: ", symbol);
   Print("Point: ", point);
   Print("Digits: ", digits);
   Print("Tick Size: ", tick_size);
   Print("Tick Value: ", tick_value);
   Print("Min Lot: ", min_lot);
   Print("Max Lot: ", max_lot);
   Print("Lot Step: ", lot_step);
   
   //--- Check current prices
   double ask = SymbolInfoDouble(symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(symbol, SYMBOL_BID);
   double spread = (ask - bid) / point;
   
   Print("Current Ask: ", ask);
   Print("Current Bid: ", bid);
   Print("Spread: ", spread, " points");
   
   if(spread > 50)
      Print("WARNING: Spread is high (", spread, " points)");
   else
      Print("OK: Spread is acceptable");
}

//+------------------------------------------------------------------+
//| Test indicator creation                                          |
//+------------------------------------------------------------------+
void TestIndicators()
{
   Print("\n--- Testing Indicators ---");
   
   string symbol = "XAUUSD";
   ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT;
   
   //--- Test Moving Averages
   int ma_fast = iMA(symbol, timeframe, 20, 0, MODE_EMA, PRICE_CLOSE);
   int ma_slow = iMA(symbol, timeframe, 50, 0, MODE_EMA, PRICE_CLOSE);
   
   if(ma_fast == INVALID_HANDLE)
      Print("ERROR: Failed to create Fast MA indicator");
   else
      Print("OK: Fast MA indicator created successfully");
      
   if(ma_slow == INVALID_HANDLE)
      Print("ERROR: Failed to create Slow MA indicator");
   else
      Print("OK: Slow MA indicator created successfully");
   
   //--- Test RSI
   int rsi = iRSI(symbol, timeframe, 14, PRICE_CLOSE);
   
   if(rsi == INVALID_HANDLE)
      Print("ERROR: Failed to create RSI indicator");
   else
      Print("OK: RSI indicator created successfully");
   
   //--- Test indicator values
   if(ma_fast != INVALID_HANDLE && ma_slow != INVALID_HANDLE && rsi != INVALID_HANDLE)
   {
      double ma_fast_buffer[3];
      double ma_slow_buffer[3];
      double rsi_buffer[3];
      
      if(CopyBuffer(ma_fast, 0, 0, 3, ma_fast_buffer) == 3 &&
         CopyBuffer(ma_slow, 0, 0, 3, ma_slow_buffer) == 3 &&
         CopyBuffer(rsi, 0, 0, 3, rsi_buffer) == 3)
      {
         Print("OK: Indicator values retrieved successfully");
         if(ShowDetails)
         {
            Print("Fast MA[0]: ", ma_fast_buffer[0]);
            Print("Slow MA[0]: ", ma_slow_buffer[0]);
            Print("RSI[0]: ", rsi_buffer[0]);
         }
      }
      else
         Print("ERROR: Failed to retrieve indicator values");
   }
   
   //--- Release handles
   IndicatorRelease(ma_fast);
   IndicatorRelease(ma_slow);
   IndicatorRelease(rsi);
}

//+------------------------------------------------------------------+
//| Test calculation functions                                       |
//+------------------------------------------------------------------+
void TestCalculations()
{
   Print("\n--- Testing Calculations ---");
   
   string symbol = "XAUUSD";
   
   //--- Test lot size calculation
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double risk_percent = 2.0;
   double sl_points = 200;
   
   Print("Account Balance: ", balance);
   Print("Risk Percent: ", risk_percent, "%");
   Print("SL Points: ", sl_points);
   
   //--- Calculate risk amount
   double risk_amount = balance * risk_percent / 100.0;
   Print("Risk Amount: ", risk_amount);
   
   //--- Calculate lot size
   double tick_value = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(symbol, SYMBOL_TRADE_TICK_SIZE);
   double point = SymbolInfoDouble(symbol, SYMBOL_POINT);
   
   if(tick_value > 0 && tick_size > 0)
   {
      double lot_size = risk_amount / (sl_points * point / tick_size * tick_value);
      
      //--- Normalize lot size
      double min_lot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MIN);
      double max_lot = SymbolInfoDouble(symbol, SYMBOL_VOLUME_MAX);
      double lot_step = SymbolInfoDouble(symbol, SYMBOL_VOLUME_STEP);
      
      lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
      lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;
      
      Print("Calculated Lot Size: ", lot_size);
      
      if(lot_size >= min_lot && lot_size <= max_lot)
         Print("OK: Lot size is within valid range");
      else
         Print("WARNING: Lot size is outside valid range");
   }
   else
      Print("ERROR: Invalid tick value or tick size");
}

//+------------------------------------------------------------------+
//| Test market conditions                                           |
//+------------------------------------------------------------------+
void TestMarketConditions()
{
   Print("\n--- Testing Market Conditions ---");
   
   string symbol = "XAUUSD";
   
   //--- Check trading mode
   long trade_mode = SymbolInfoInteger(symbol, SYMBOL_TRADE_MODE);
   Print("Trade Mode: ", trade_mode);
   
   if(trade_mode == SYMBOL_TRADE_MODE_DISABLED)
      Print("WARNING: Trading is disabled for ", symbol);
   else
      Print("OK: Trading is allowed for ", symbol);
   
   //--- Check market hours
   datetime current_time = TimeCurrent();
   MqlDateTime dt;
   TimeToStruct(current_time, dt);
   
   Print("Current Server Time: ", TimeToString(current_time));
   Print("Current Hour: ", dt.hour);
   
   //--- Check if within trading hours (8-22)
   if(dt.hour >= 8 && dt.hour <= 22)
      Print("OK: Within trading hours");
   else
      Print("INFO: Outside recommended trading hours");
   
   //--- Check spread
   double ask = SymbolInfoDouble(symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(symbol, SYMBOL_BID);
   double point = SymbolInfoDouble(symbol, SYMBOL_POINT);
   double spread = (ask - bid) / point;
   
   Print("Current Spread: ", spread, " points");
   
   if(spread <= 50)
      Print("OK: Spread is acceptable for trading");
   else
      Print("WARNING: Spread is too high for trading");
}

//+------------------------------------------------------------------+
