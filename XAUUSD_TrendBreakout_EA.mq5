//+------------------------------------------------------------------+
//|                                        XAUUSD_TrendBreakout_EA.mq5 |
//|                                  Copyright 2024, TradingAI_MQ5 |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, TradingAI_MQ5"
#property link "https://www.mql5.com"
#property version "1.00"
#property description "XAUUSD Trend Following & Breakout Expert Advisor"

//--- Include libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//--- Create objects
CTrade trade;
CPositionInfo position;
COrderInfo order;

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input group "=== TRADING SETTINGS ===" input double LotSize = 0.01; // Lot size
input int MagicNumber = 123456;                                     // Magic number
input string TradeComment = "XAUUSD_EA";                            // Trade comment

input group "=== STRATEGY PARAMETERS ===" input int MA_Fast_Period = 20; // Fast MA Period
input int MA_Slow_Period = 50;                                           // Slow MA Period
input int RSI_Period = 14;                                               // RSI Period
input double RSI_Overbought = 70;                                        // RSI Overbought Level
input double RSI_Oversold = 30;                                          // RSI Oversold Level

input group "=== BREAKOUT SETTINGS ===" input int Breakout_Lookback = 20; // Lookback periods for H/L
input double Breakout_Buffer = 10;                                        // Breakout buffer in points

input group "=== RISK MANAGEMENT ===" input double StopLoss_Points = 200; // Stop Loss in points
input double TakeProfit_Points = 400;                                     // Take Profit in points
input double TrailingStop_Points = 150;                                   // Trailing Stop in points
input double MaxRisk_Percent = 2.0;                                       // Max risk per trade (%)

input group "=== TIME FILTER ===" input bool UseTimeFilter = true; // Use time filter
input int StartHour = 8;                                           // Start trading hour (server time)
input int EndHour = 22;                                            // End trading hour (server time)

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
int ma_fast_handle;
int ma_slow_handle;
int rsi_handle;
double ma_fast_buffer[];
double ma_slow_buffer[];
double rsi_buffer[];
datetime last_bar_time;
bool new_bar;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Set magic number
   trade.SetExpertMagicNumber(MagicNumber);

   //--- Initialize indicators
   ma_fast_handle = iMA(_Symbol, PERIOD_CURRENT, MA_Fast_Period, 0, MODE_EMA, PRICE_CLOSE);
   ma_slow_handle = iMA(_Symbol, PERIOD_CURRENT, MA_Slow_Period, 0, MODE_EMA, PRICE_CLOSE);
   rsi_handle = iRSI(_Symbol, PERIOD_CURRENT, RSI_Period, PRICE_CLOSE);

   //--- Check if indicators are created successfully
   if (ma_fast_handle == INVALID_HANDLE || ma_slow_handle == INVALID_HANDLE || rsi_handle == INVALID_HANDLE)
   {
      Print("Error creating indicators");
      return INIT_FAILED;
   }

   //--- Set array as series
   ArraySetAsSeries(ma_fast_buffer, true);
   ArraySetAsSeries(ma_slow_buffer, true);
   ArraySetAsSeries(rsi_buffer, true);

   //--- Initialize variables
   last_bar_time = iTime(_Symbol, PERIOD_CURRENT, 0);

   Print("XAUUSD Trend Breakout EA initialized successfully");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Release indicator handles
   IndicatorRelease(ma_fast_handle);
   IndicatorRelease(ma_slow_handle);
   IndicatorRelease(rsi_handle);

   Print("XAUUSD Trend Breakout EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- Check for new bar
   CheckNewBar();

   //--- Only trade on new bar
   if (!new_bar)
      return;

   //--- Check time filter
   if (UseTimeFilter && !IsTimeToTrade())
      return;

   //--- Check market conditions
   if (!IsMarketSuitable())
      return;

   //--- Update indicator values
   if (!UpdateIndicators())
      return;

   //--- Check for trailing stop
   TrailingStop();

   //--- Check for trading signals
   CheckTradingSignals();
}

//+------------------------------------------------------------------+
//| Check for new bar                                               |
//+------------------------------------------------------------------+
void CheckNewBar()
{
   datetime current_bar_time = iTime(_Symbol, PERIOD_CURRENT, 0);
   new_bar = (current_bar_time != last_bar_time);
   if (new_bar)
      last_bar_time = current_bar_time;
}

//+------------------------------------------------------------------+
//| Check if it's time to trade                                     |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
   MqlDateTime dt;
   TimeToStruct(TimeCurrent(), dt);

   if (StartHour <= EndHour)
      return (dt.hour >= StartHour && dt.hour <= EndHour);
   else
      return (dt.hour >= StartHour || dt.hour <= EndHour);
}

//+------------------------------------------------------------------+
//| Update indicator values                                          |
//+------------------------------------------------------------------+
bool UpdateIndicators()
{
   //--- Copy indicator values
   if (CopyBuffer(ma_fast_handle, 0, 0, 3, ma_fast_buffer) < 3)
      return false;
   if (CopyBuffer(ma_slow_handle, 0, 0, 3, ma_slow_buffer) < 3)
      return false;
   if (CopyBuffer(rsi_handle, 0, 0, 3, rsi_buffer) < 3)
      return false;

   return true;
}

//+------------------------------------------------------------------+
//| Check for trading signals                                       |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
   //--- Don't open new positions if we already have one
   if (PositionsTotal() > 0)
      return;

   //--- Get current market data
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

   //--- Calculate breakout levels
   double highest = iHigh(_Symbol, PERIOD_CURRENT, iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, Breakout_Lookback, 1));
   double lowest = iLow(_Symbol, PERIOD_CURRENT, iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, Breakout_Lookback, 1));

   //--- Add buffer to breakout levels
   double breakout_high = highest + Breakout_Buffer * _Point;
   double breakout_low = lowest - Breakout_Buffer * _Point;

   //--- Check for BUY signal
   if (IsBuySignal(ask, breakout_high))
   {
      OpenBuyPosition(ask);
   }
   //--- Check for SELL signal
   else if (IsSellSignal(bid, breakout_low))
   {
      OpenSellPosition(bid);
   }
}

//+------------------------------------------------------------------+
//| Check BUY signal conditions                                     |
//+------------------------------------------------------------------+
bool IsBuySignal(double price, double breakout_level)
{
   //--- Trend condition: Fast MA > Slow MA
   bool trend_up = ma_fast_buffer[0] > ma_slow_buffer[0];

   //--- RSI condition: Not overbought
   bool rsi_ok = rsi_buffer[0] < RSI_Overbought;

   //--- Breakout condition
   bool breakout = price > breakout_level;

   //--- MA crossover condition
   bool ma_cross = (ma_fast_buffer[1] <= ma_slow_buffer[1]) && (ma_fast_buffer[0] > ma_slow_buffer[0]);

   return (trend_up && rsi_ok && breakout) || (ma_cross && rsi_ok);
}

//+------------------------------------------------------------------+
//| Check SELL signal conditions                                    |
//+------------------------------------------------------------------+
bool IsSellSignal(double price, double breakout_level)
{
   //--- Trend condition: Fast MA < Slow MA
   bool trend_down = ma_fast_buffer[0] < ma_slow_buffer[0];

   //--- RSI condition: Not oversold
   bool rsi_ok = rsi_buffer[0] > RSI_Oversold;

   //--- Breakout condition
   bool breakout = price < breakout_level;

   //--- MA crossover condition
   bool ma_cross = (ma_fast_buffer[1] >= ma_slow_buffer[1]) && (ma_fast_buffer[0] < ma_slow_buffer[0]);

   return (trend_down && rsi_ok && breakout) || (ma_cross && rsi_ok);
}

//+------------------------------------------------------------------+
//| Open BUY position                                               |
//+------------------------------------------------------------------+
void OpenBuyPosition(double price)
{
   //--- Calculate lot size based on risk
   double lot_size = CalculateLotSize(StopLoss_Points);

   //--- Calculate SL and TP
   double sl = price - StopLoss_Points * _Point;
   double tp = price + TakeProfit_Points * _Point;

   //--- Normalize prices
   sl = NormalizeDouble(sl, _Digits);
   tp = NormalizeDouble(tp, _Digits);

   //--- Open position
   if (trade.Buy(lot_size, _Symbol, price, sl, tp, TradeComment))
   {
      Print("BUY order opened successfully at ", price, " SL: ", sl, " TP: ", tp);
   }
   else
   {
      Print("Error opening BUY order: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
   }
}

//+------------------------------------------------------------------+
//| Open SELL position                                              |
//+------------------------------------------------------------------+
void OpenSellPosition(double price)
{
   //--- Calculate lot size based on risk
   double lot_size = CalculateLotSize(StopLoss_Points);

   //--- Calculate SL and TP
   double sl = price + StopLoss_Points * _Point;
   double tp = price - TakeProfit_Points * _Point;

   //--- Normalize prices
   sl = NormalizeDouble(sl, _Digits);
   tp = NormalizeDouble(tp, _Digits);

   //--- Open position
   if (trade.Sell(lot_size, _Symbol, price, sl, tp, TradeComment))
   {
      Print("SELL order opened successfully at ", price, " SL: ", sl, " TP: ", tp);
   }
   else
   {
      Print("Error opening SELL order: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage                     |
//+------------------------------------------------------------------+
double CalculateLotSize(double sl_points)
{
   //--- Get account information
   double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double risk_amount = account_balance * MaxRisk_Percent / 100.0;

   //--- Calculate tick value
   double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

   //--- Calculate lot size
   double lot_size = risk_amount / (sl_points * _Point / tick_size * tick_value);

   //--- Get min and max lot size
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

   //--- Normalize lot size
   lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
   lot_size = NormalizeDouble(lot_size / lot_step, 0) * lot_step;

   //--- Use fixed lot size if calculated size is too small
   if (lot_size < min_lot)
      lot_size = LotSize;

   return lot_size;
}

//+------------------------------------------------------------------+
//| Trailing Stop function                                          |
//+------------------------------------------------------------------+
void TrailingStop()
{
   //--- Loop through all positions
   for (int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if (position.SelectByIndex(i))
      {
         //--- Check if position belongs to this EA
         if (position.Magic() != MagicNumber || position.Symbol() != _Symbol)
            continue;

         double current_price;
         double new_sl;

         if (position.PositionType() == POSITION_TYPE_BUY)
         {
            current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
            new_sl = current_price - TrailingStop_Points * _Point;

            //--- Move SL only if new SL is higher than current SL
            if (new_sl > position.StopLoss() && new_sl < current_price)
            {
               new_sl = NormalizeDouble(new_sl, _Digits);
               if (trade.PositionModify(position.Ticket(), new_sl, position.TakeProfit()))
               {
                  Print("Trailing stop updated for BUY position. New SL: ", new_sl);
               }
            }
         }
         else if (position.PositionType() == POSITION_TYPE_SELL)
         {
            current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            new_sl = current_price + TrailingStop_Points * _Point;

            //--- Move SL only if new SL is lower than current SL
            if ((position.StopLoss() == 0 || new_sl < position.StopLoss()) && new_sl > current_price)
            {
               new_sl = NormalizeDouble(new_sl, _Digits);
               if (trade.PositionModify(position.Ticket(), new_sl, position.TakeProfit()))
               {
                  Print("Trailing stop updated for SELL position. New SL: ", new_sl);
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Get current spread in points                                    |
//+------------------------------------------------------------------+
double GetSpreadPoints()
{
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   return (ask - bid) / _Point;
}

//+------------------------------------------------------------------+
//| Check if market conditions are suitable for trading            |
//+------------------------------------------------------------------+
bool IsMarketSuitable()
{
   //--- Check spread
   double spread = GetSpreadPoints();
   if (spread > 50) // Max spread 5 pips for XAUUSD
   {
      Print("Spread too high: ", spread, " points");
      return false;
   }

   //--- Check if market is open
   if (!SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE))
   {
      Print("Trading is not allowed for ", _Symbol);
      return false;
   }

   return true;
}
