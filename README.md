# XAUUSD Trading Bot MQL5

Bot trading otomatis untuk pair XAUUSD (Gold vs USD) yang dibangun dengan MQL5 untuk platform MetaTrader 5.

## 📋 Deskripsi

Expert Advisor ini menggunakan kombinasi strategi **Trend Following** dan **Breakout** yang dirancang khusus untuk karakteristik trading emas (XAUUSD). Bot ini dilengkapi dengan sistem risk management yang komprehensif dan filter waktu trading yang optimal.

## 🎯 Fitur Utama

### Strategi Trading
- **Trend Following**: Menggunakan EMA 20 & 50 untuk identifikasi trend
- **RSI Filter**: RSI 14 periode untuk konfirmasi momentum
- **Breakout Strategy**: Entry pada breakout level support/resistance
- **Time Filter**: Trading hanya pada jam-jam optimal (London & New York session)

### Risk Management
- **Position Sizing**: Otomatis berdasarkan persentase risiko
- **Stop Loss**: 20 pips (dapat disesuaikan)
- **Take Profit**: 40 pips (Risk:Reward = 1:2)
- **Trailing Stop**: 15 pips untuk memaksimalkan profit
- **Spread Filter**: Maksimal 5 pips untuk menghindari spread tinggi

## 📁 File dalam Proyek

```
tradingai_mq5/
├── XAUUSD_TrendBreakout_EA.mq5      # File utama Expert Advisor
├── Test_XAUUSD_EA.mq5               # Script testing untuk EA
├── XAUUSD_EA_Optimization.set       # File konfigurasi optimisasi
├── XAUUSD_EA_Documentation.md       # Dokumentasi lengkap
└── README.md                        # File ini
```

## 🚀 Quick Start

### 1. Instalasi
1. Copy file `XAUUSD_TrendBreakout_EA.mq5` ke folder `MQL5/Experts/`
2. Compile EA di MetaEditor (F7)
3. Restart MetaTrader 5

### 2. Setup Trading
1. Buka chart XAUUSD
2. Set timeframe M15, M30, atau H1
3. Drag & drop EA ke chart
4. Sesuaikan parameter (lihat dokumentasi)
5. Enable AutoTrading

### 3. Parameter Dasar
```
LotSize = 0.01
MaxRisk_Percent = 1.0
StopLoss_Points = 200
TakeProfit_Points = 400
UseTimeFilter = true
```

## 📊 Backtesting

### Pengaturan Backtesting
- **Period**: Minimal 1 tahun data
- **Timeframe**: M15 atau M30
- **Spread**: 3-5 pips (realistis)
- **Model**: Every tick (jika tersedia)

### Jalankan Test Script
1. Compile `Test_XAUUSD_EA.mq5`
2. Jalankan script pada chart XAUUSD
3. Periksa output di tab "Experts"

## ⚙️ Optimisasi

### Parameter Utama untuk Optimisasi
- **MA_Fast_Period**: 15-25
- **MA_Slow_Period**: 40-60  
- **StopLoss_Points**: 150-250
- **TakeProfit_Points**: 300-500

### Menggunakan File .set
1. Load file `XAUUSD_EA_Optimization.set` di Strategy Tester
2. Enable optimisasi untuk parameter yang ditandai
3. Jalankan optimisasi dengan genetic algorithm

## 📈 Performa yang Diharapkan

### Kondisi Optimal
- **Timeframe**: M15-H1
- **Waktu Trading**: 08:00-22:00 (server time)
- **Spread**: < 5 pips
- **Volatilitas**: Medium-High

### Target Metrics
- **Win Rate**: 55-65%
- **Risk:Reward**: 1:2
- **Max Drawdown**: < 15%
- **Profit Factor**: > 1.3

## ⚠️ Risk Warning

- **Risiko Tinggi**: Trading forex dan CFD memiliki risiko tinggi
- **Demo First**: Selalu test di demo account terlebih dahulu
- **Money Management**: Jangan risk lebih dari 2% per trade
- **Market Conditions**: EA bekerja optimal pada kondisi trending market

## 🛠️ Troubleshooting

### EA Tidak Trading
- ✅ Pastikan AutoTrading aktif
- ✅ Cek spread tidak terlalu tinggi
- ✅ Verifikasi time filter settings
- ✅ Periksa log untuk error messages

### Performance Buruk
- 📊 Lakukan re-optimisasi parameter
- 📊 Cek kondisi market (trending vs ranging)
- 📊 Sesuaikan risk management
- 📊 Pertimbangkan timeframe berbeda

## 📚 Dokumentasi Lengkap

Untuk panduan detail, baca file `XAUUSD_EA_Documentation.md` yang berisi:
- Penjelasan strategi lengkap
- Panduan optimisasi parameter
- Tips trading XAUUSD
- FAQ dan troubleshooting

## 📞 Support

Untuk pertanyaan dan support:
- 📧 Email: [<EMAIL>]
- 💬 Telegram: [@your-telegram]
- 🌐 Website: [your-website.com]

## 📄 License

Copyright 2024 TradingAI_MQ5. All rights reserved.

---

**Disclaimer**: EA ini dibuat untuk tujuan edukasi dan testing. Selalu lakukan backtesting dan forward testing sebelum menggunakan dengan uang riil. Developer tidak bertanggung jawab atas kerugian yang mungkin terjadi.

## 🔄 Version History

- **v1.0** (2024): Initial release
  - Trend following + breakout strategy
  - Risk management system
  - Time filter
  - Trailing stop feature
